<?php

namespace Theme25\Backend\Controller\Sale\Order;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'order-edit.js',
        ]);
    }

    public function prepareOrderForm() {
        $order_id = (int)$this->requestGet('order_id');

        if (!$order_id) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            exit;
        }


        $this->prepareOrderData($order_id)
             ->prepareOrderProducts($order_id)
             ->prepareOrderTotals($order_id)
             ->prepareOrderVouchers($order_id)
             ->prepareOrderCoupons($order_id)
             ->prepareCustomerData($order_id)
             ->prepareOrderStatuses()
             ->preparePaymentMethods()
             ->prepareShippingMethods();

        $this->setData([
            'back_url' => $this->getAdminLink('sale/order'),
            'save_url' => $this->getAdminLink('sale/order/edit', 'order_id=' . $order_id),
            'order_id' => $order_id
        ]);
    }

    /**
     * Обработва актуализацията на поръчката
     *
     * @param int $order_id ID на поръчката
     */
    public function processOrderUpdate($order_id) {
        if (!$this->hasPermission('modify', 'sale/order')) {
            $this->setSession('error', 'Няmate права за редактиране на поръчки');
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return;
        }

        $json = [];

        $this->loadModelAs('sale/order', 'orders');

        try {
            // Получаване на данните от формата
            $data = $this->processAjaxPost();

            $validation = $this->validateOrderData($data);

            // Валидация на данните
            if( $validation === true ) {

                // Забрана за запазване без продукти
                if (empty($data['products']) || count($data['products']) === 0) {
                    $json['error'] = 'Поръчката трябва да съдържа поне един продукт';
                    $this->setJSONResponseOutput($json);
                    return;
                }

                $order_data = $this->prepareOrderDataForUpdate($order_id, $data, $json);

                // Актуализиране на поръчката
                //$this->orders->editOrder($order_id, $data);

                // Добавяне на история, ако е променен статуса
                // if (isset($data['order_status_id']) && !empty($data['order_status_id'])) {
                //     $comment = $data['comment'] ?? '';
                //     $notify = isset($data['notify']) ? (bool)$data['notify'] : false;

                //     $this->orders->addOrderHistory($order_id, $data['order_status_id'], $comment, $notify);
                // }

                $json['success'] = 'Поръчката е актуализирана успешно';
            } else {
                $json['error'] = 'Невалидни данни за актуализиране на поръчката<br>' . $validation;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при актуализиране на поръчката: ' . $e->getMessage();
        }
        $this->setJSONResponseOutput($json);
    }

    private function prepareOrderDataForUpdate($order_id, $data=[], &$json=[]) {
        $this->prepareOrderData($order_id)
             ->prepareOrderProducts($order_id)
             ->prepareOrderVouchers($order_id)
             ->prepareOrderCoupons($order_id);
        $order_data = array_merge($this->getData(), $data);

        unset($order_data['user_token'], $order_data['notify']);

        $fields_rules = $this->orders->getFieldsRules();

        $data_for_update = [];
        foreach ($fields_rules['order'] as $key => $type) {
            if (isset($order_data[$key])) {
                $data_for_update[$key] = $order_data[$key];
            }
        }

        foreach ($order_data['products'] as $key => $product) {
            $data_for_update['products'][] = $this->processOrderedProduct($fields_rules,$product);
        }

        $data_for_update['vouchers'] = !empty($order_data['order_vouchers']) ? $order_data['order_vouchers'] : [];

        if(empty($data_for_update['vouchers']) && !empty($data['voucher'])) {
           $result = $this->applyVoucher($order_id, $data['voucher']);
           if(!empty($result['success'])) {
                $this->prepareOrderVouchers($order_id);
                $data_for_update['vouchers'] = $this->getData('order_vouchers');
           }
           else {
                $json['error'] = $result['error'];
           }
        }

        if(empty($order_data['order_coupon']) && !empty($data['coupon'])) {
            $result = $this->applyCoupon($order_id, $data['coupon']);
            if(!empty($result['success'])) {
                $this->prepareOrderCoupons($order_id);
                // $data_for_update['coupon'] = $this->getData('order_coupon');
            }
            else {
                $json['error'] = $result['error'];
            }
        }

        $data_for_update['totals'] = [];
        $order_totals = $this->calculateTotalsFromProducts($order_id, $data_for_update['products']);

        F()->log->developer($order_totals, __FILE__, __LINE__);

        // foreach ($order_totals as $key => $total) {
        //     $data_for_update['totals'][$key] = [];
        //     foreach ($fields_rules['order_total'] as $rule_key => $type) {
        //         if (isset($total[$rule_key])) {
        //             $data_for_update['totals'][$key][$rule_key] = $total[$rule_key];
        //         }
        //     }
        // }

        F()->log->developer($data_for_update, __FILE__, __LINE__);


        return $data_for_update;
    }

    private function processOrderedProduct($fields_rules,$ordered_product) {
        $product = [];
        foreach ($fields_rules['order_product'] as $rule_key => $type) {
            if (isset($ordered_product[$rule_key])) {
                $product[$rule_key] = $ordered_product[$rule_key];
            }
        }
        $product['options'] = !empty($ordered_product['options']) ? $ordered_product['options'] : [];
        $product['options'] = $this->processOrderedProductOptions($fields_rules,$product);
        return $product;
    }

    private function processOrderedProductOptions($fields_rules,$ordered_product) {
        $options = [];
        if(!empty($ordered_product['options'])) {
            foreach ($ordered_product['options'] as $option) {
                $option_data = [];
                foreach ($fields_rules['order_option'] as $rule_key => $type) {
                    if (isset($option[$rule_key])) {
                        $value = $option[$rule_key];
                        if($rule_key == 'name' || $rule_key == 'value') {
                            $value = trim($value);
                        }
                        $option_data[$rule_key] = $value;
                    }
                }
                $options[] = $option_data;
            }
        }
        return $options;
    }


    /**
     * Валидира данните за поръчката
     *
     * @param array $data Данни за валидация
     * @return bool Резултат от валидацията
     */
    private function validateOrderData($data) {
        $errors = [];

        // Валидация на задължителни полета
        if (empty($data['firstname'])) {
            $errors[] = 'Името е задължително';
        }

        if (empty($data['lastname'])) {
            $errors[] = 'Фамилията е задължителна';
        }

        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Валиден имейл е задължителен';
        }

        if (!empty($errors)) {
            return implode('<br>', $errors);
        }

        return true;
    }

    /**
     * Подготвя основните данни за поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderData($order_id) {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'sale/order' => 'orders'
        ]);

        // Получаване на информацията за поръчката
        $order_info = $this->orders->getOrder($order_id);

        if (!$order_info) {
            $this->redirectResponse($this->getAdminLink('sale/order'));
            return $this;
        }

        // Подготовка на данните за поръчката
        $order_data = [
            'order_id' => $order_info['order_id'],
            'customer_id' => $order_info['customer_id'],
            'customer_group_id' => $order_info['customer_group_id'],
            'firstname' => $order_info['firstname'],
            'lastname' => $order_info['lastname'],
            'email' => $order_info['email'],
            'telephone' => $order_info['telephone'],
            'fax' => $order_info['fax'],
            'order_status_id' => $order_info['order_status_id'],
            'comment' => $order_info['comment'],
            'total' => $order_info['total'],
            'currency_code' => $order_info['currency_code'],
            'currency_value' => $order_info['currency_value'],
            'date_added' => $order_info['date_added'],
            'date_modified' => $order_info['date_modified'],

            // Адрес за плащане
            'payment_firstname' => $order_info['payment_firstname'],
            'payment_lastname' => $order_info['payment_lastname'],
            'payment_company' => $order_info['payment_company'],
            'payment_address_1' => $order_info['payment_address_1'],
            'payment_address_2' => $order_info['payment_address_2'],
            'payment_city' => $order_info['payment_city'],
            'payment_postcode' => $order_info['payment_postcode'],
            'payment_country_id' => $order_info['payment_country_id'],
            'payment_zone_id' => $order_info['payment_zone_id'],
            'payment_method' => $order_info['payment_method'],
            'payment_code' => $order_info['payment_code'],

            // Адрес за доставка
            'shipping_firstname' => $order_info['shipping_firstname'],
            'shipping_lastname' => $order_info['shipping_lastname'],
            'shipping_company' => $order_info['shipping_company'],
            'shipping_address_1' => $order_info['shipping_address_1'],
            'shipping_address_2' => $order_info['shipping_address_2'],
            'shipping_city' => $order_info['shipping_city'],
            'shipping_postcode' => $order_info['shipping_postcode'],
            'shipping_country_id' => $order_info['shipping_country_id'],
            'shipping_zone_id' => $order_info['shipping_zone_id'],
            'shipping_method' => $order_info['shipping_method'],
            'shipping_code' => $order_info['shipping_code']
        ];

        $this->setData($order_data);

        return $this;
    }

    /**
     * Подготвя продуктите в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderProducts($order_id) {
        // Получаване на продуктите в поръчката
        $order_products = $this->orders->getOrderProducts($order_id);

        // Зареждане на модела за изображения
        $this->loadModelAs('tool/image', 'imageModel');
        $this->loadModelAs('catalog/product', 'productModel');

        $products = [];

        foreach ($order_products as $product) {
            // Получаване на опциите за продукта
            $options = $this->orders->getOrderOptions($order_id, $product['order_product_id']);

            $product_options = [];
            foreach ($options as $option) {
                $product_options[] = [
                    'name' => trim($option['name']),
                    'value' => $option['value'],
                    'product_option_id' => $option['product_option_id'],
                    'product_option_value_id' => $option['product_option_value_id']
                ];
            }

            // Получаване на изображението на продукта
            $product_info = $this->productModel->getProduct($product['product_id']);
            $product_image = 'no_image.png';

            if ($product_info && !empty($product_info['image'])) {
                $product_image = $product_info['image'];
            }

            $products[] = [
                'order_product_id' => $product['order_product_id'],
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'quantity' => $product['quantity'],
                'price' => $product['price'],
                'total' => $product['total'],
                'tax' => $product['tax'],
                'reward' => $product['reward'],
                'options' => $product_options,
                'image' => $this->imageModel->resize($product_image, 80, 80),
                'image_thumb' => $this->imageModel->resize($product_image, 50, 50)
            ];
        }

        $this->setData('order_products', $products);

        return $this;
    }

    /**
     * Подготвя общите суми на поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderTotals($order_id) {
        $this->loadModelAs('sale/order', 'orders');
        
        // Получаване на общите суми
        $order_totals = $this->orders->getOrderTotals($order_id);

        $totals = [];
        $totals_by_code = [];

        // Променливи за динамично изчисляване на крайната сума
        $subtotal = 0;
        $shipping = 0;
        $tax = 0;
        $voucher = 0;
        $coupon = 0;
        $other_totals = 0;

        // Групиране на сумите по код и събиране на компонентите
        foreach ($order_totals as $total) {
            $value = (float)$total['value'];

            // Събиране на компонентите за изчисляване на крайната сума
            switch ($total['code']) {
                case 'sub_total':
                    $subtotal = $value;
                    break;
                case 'shipping':
                    $shipping = $value;
                    break;
                case 'tax':
                    $tax = $value;
                    break;
                case 'voucher':
                    $voucher = $value; // Вече отрицателна стойност
                    break;
                case 'coupon':
                    $coupon = $value; // Вече отрицателна стойност
                    break;
                case 'total':
                    // Пропускаме total - ще го изчислим динамично
                    break;
                default:
                    // Други суми (handling, low_order_fee, etc.)
                    $other_totals += $value;
                    break;
            }

            // Запазване на оригиналните данни (освен total)
            if ($total['code'] !== 'total') {
                $totals_by_code[$total['code']] = [
                    'title' => $total['title'],
                    'text' => $this->formatCurrency($value, $this->getData('currency_code'), $this->getData('currency_value')),
                    'value' => $value,
                    'code' => $total['code'],
                    'sort_order' => $total['sort_order']
                ];
            }
        }

        // Динамично изчисляване на крайната сума
        $calculated_total = $subtotal + $shipping + $tax + $other_totals + $voucher + $coupon;
        $calculated_total = max(0, $calculated_total); // Не може да бъде отрицателна

        // Добавяне на изчислената крайна сума
        $totals_by_code['total'] = [
            'title' => 'Общо',
            'text' => $this->formatCurrency($calculated_total, $this->getData('currency_code'), $this->getData('currency_value')),
            'value' => $calculated_total,
            'code' => 'total',
            'sort_order' => 99
        ];

        // Подреждане в правилния ред
        $order_sequence = ['sub_total', 'shipping', 'tax', 'voucher', 'coupon', 'total'];

        foreach ($order_sequence as $code) {
            if (isset($totals_by_code[$code])) {
                $totals[] = $totals_by_code[$code];
            }
        }

        // Добавяне на останалите суми, които не са в основната последователност
        foreach ($totals_by_code as $code => $total) {
            if (!in_array($code, $order_sequence)) {
                // Вмъкване преди total
                if (end($totals)['code'] === 'total') {
                    array_splice($totals, -1, 0, [$total]);
                } else {
                    $totals[] = $total;
                }
            }
        }

        $this->setData('order_totals', $totals);

        return $this;
    }

    /**
     * Подготвя ваучерите в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderVouchers($order_id) {
        // Получаване на ваучерите в поръчката
        $order_vouchers = $this->orders->getOrderVouchers($order_id);

        $vouchers = [];
        $voucher_code = '';

        foreach ($order_vouchers as $voucher) {
            $vouchers[] = [
                'voucher_id' => $voucher['voucher_id'],
                'description' => $voucher['description'],
                'code' => $voucher['code'],
                'from_name' => $voucher['from_name'],
                'from_email' => $voucher['from_email'],
                'to_name' => $voucher['to_name'],
                'to_email' => $voucher['to_email'],
                'amount' => $voucher['amount'],
                'amount_text' => $this->formatCurrency($voucher['amount'], $this->getData('currency_code'), $this->getData('currency_value'))
            ];

            // Запазване на първия ваучер код за полето във формата
            if (empty($voucher_code)) {
                $voucher_code = $voucher['code'];
            }
        }

        $this->setData([
            'order_vouchers' => $vouchers,
            'voucher_code' => $voucher_code
        ]);

        return $this;
    }

    /**
     * Подготвя купоните в поръчката
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderCoupons($order_id) {
        // Получаване на купон информация от order_totals таблицата
        $coupon_code = '';
        $coupon_info = [];

        // Търсене на купон в order_totals
        $order_totals = $this->orders->getOrderTotals($order_id);

        foreach ($order_totals as $total) {
            if ($total['code'] === 'coupon') {
                // Извличане на купон кода от заглавието
                if (preg_match('/\(([^)]+)\)/', $total['title'], $matches)) {
                    $coupon_code = $matches[1];
                }

                $coupon_info = [
                    'code' => $coupon_code,
                    'title' => $total['title'],
                    'value' => $total['value'],
                    'value_text' => $this->formatCurrency($total['value'], $this->getData('currency_code'), $this->getData('currency_value'))
                ];
                break;
            }
        }

        $this->setData([
            'order_coupon' => $coupon_info,
            'coupon_code' => $coupon_code
        ]);

        return $this;
    }

    /**
     * Подготвя данните за клиента
     *
     * @param int $order_id ID на поръчката
     * @return $this За верижно извикване на методи
     */
    private function prepareCustomerData($order_id) {
        $customer_id = $this->getData('customer_id');

        if ($customer_id) {
            $this->loadModelAs('customer/customer', 'customers');
            $customer_info = $this->customers->getCustomer($customer_id);

            if ($customer_info) {
                $this->setData('customer_info', $customer_info);
            }
        }

        return $this;
    }

    /**
     * Подготвя статусите на поръчки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderStatuses() {
        // Зареждане на модела за статуси
        $this->loadModelAs('localisation/order_status', 'orderStatuses');

        // Получаване на всички статуси
        $order_statuses = $this->orderStatuses->getOrderStatuses();

        $this->setData('order_statuses', $order_statuses);

        return $this;
    }

    /**
     * Подготвя методите за плащане
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePaymentMethods() {
        // Тук може да се добави логика за зареждане на методи за плащане
        $payment_methods = [
            ['code' => 'bank_transfer', 'name' => 'Банков превод'],
            ['code' => 'cod', 'name' => 'Наложен платеж'],
            ['code' => 'credit_card', 'name' => 'Кредитна карта']
        ];

        $this->setData('payment_methods', $payment_methods);

        return $this;
    }

    /**
     * Подготвя методите за доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareShippingMethods() {
        // Тук може да се добави логика за зареждане на методи за доставка
        $shipping_methods = [
            ['code' => 'flat', 'name' => 'Фиксирана доставка'],
            ['code' => 'pickup', 'name' => 'Вземане от офис'],
            ['code' => 'free', 'name' => 'Безплатна доставка']
        ];

        $this->setData('shipping_methods', $shipping_methods);

        return $this;
    }

    /**
     * AJAX метод за търсене на продукти
     */
    public function searchProducts() {
        $this->initAdminData();

        $search = $this->requestPost('search', '');
        $limit = (int)$this->requestPost('limit', 20);
        $start = (int)$this->requestPost('start', 0);

        $this->loadModelAs('catalog/product', 'productModel');
        $this->loadModelAs('tool/image', 'imageModel');

        $filter_data = [
            'filter_name' => $search,
            'start' => $start,
            'limit' => $limit
        ];

        $products = $this->productModel->getProducts($filter_data);
        $results = [];

        foreach ($products as $product) {
            $image = 'no_image.png';
            if (!empty($product['image'])) {
                $image = $product['image'];
            }

            $results[] = [
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'price' => number_format($product['price'], 2, '.', ''),
                'image' => $this->imageModel->resize($image, 40, 40),
                'image_large' => $this->imageModel->resize($image, 80, 80)
            ];
        }

        // Проверка дали има още продукти
        $total_products = $this->productModel->getTotalProducts($filter_data);
        $has_more = ($start + $limit) < $total_products;

        header('Content-Type: application/json');
        echo json_encode([
            'products' => $results,
            'has_more' => $has_more,
            'total' => $total_products
        ]);
        exit;
    }

    /**
     * AJAX метод за получаване на опциите на продукт
     */
    public function getProductOptions() {
        $this->initAdminData();

        $product_id = (int)$this->requestPost('product_id', 0);

        if (!$product_id) {
            header('Content-Type: application/json');
            echo json_encode(['options' => []]);
            exit;
        }

        $this->loadModelAs('catalog/product', 'productModel');

        $product_options = $this->productModel->getProductOptions($product_id);
        $options = [];

        foreach ($product_options as $product_option) {
            $option_values = [];

            // Проверка дали има стойности за опцията
            if (isset($product_option['product_option_value']) && is_array($product_option['product_option_value'])) {
                foreach ($product_option['product_option_value'] as $option_value) {
                    $option_values[] = [
                        'product_option_value_id' => $option_value['product_option_value_id'],
                        'option_value_id' => $option_value['option_value_id'],
                        'name' => trim($option_value['name']),
                        'price' => $option_value['price'],
                        'price_prefix' => $option_value['price_prefix']
                    ];
                }
            }

            $options[] = [
                'product_option_id' => $product_option['product_option_id'],
                'option_id' => $product_option['option_id'],
                'name' => $product_option['name'],
                'type' => $product_option['type'],
                'required' => $product_option['required'],
                'option_values' => $option_values
            ];
        }

        header('Content-Type: application/json');
        echo json_encode(['options' => $options]);
        exit;
    }

    /**
     * AJAX метод за прилагане на ваучер код
     */
    public function applyVoucher($order_id = 0, $voucher_code = '') {
        
        $flag_no_ajax = false;
        if($order_id && $voucher_code) {
            $flag_no_ajax = true;
        }
        else {
            $this->initAdminData();
        }

        $order_id = $order_id ? $order_id : (int)$this->requestPost('order_id', 0);
        $voucher_code = $voucher_code ? $voucher_code : trim($this->requestPost('voucher_code', ''));

        $json = [];

        if (!$order_id) {
            $json['error'] = 'Невалиден ID на поръчка';
        } elseif (empty($voucher_code)) {
            $json['error'] = 'Моля въведете ваучер код';
        } else {
            try {
                // Опит за зареждане на модела за ваучери
                try {
                    $this->loadModelAs('extension/total/voucher', 'voucherModel');
                    $voucher_info = $this->voucherModel->getVoucher($voucher_code);
                } catch (Exception $e) {
                    // Fallback - директна проверка в базата данни
                    $voucher_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "voucher WHERE code = '" . $this->db->escape($voucher_code) . "' AND status = '1'");
                    $voucher_info = $voucher_query->num_rows ? $voucher_query->row : false;
                }

                if ($voucher_info && (isset($voucher_info['status']) ? $voucher_info['status'] : true)) {
                    // Прилагане на ваучера към поръчката
                    $this->applyVoucherToOrder($order_id, $voucher_code, $voucher_info);

                    // Преизчисляване на общите суми
                    $updated_totals = $this->recalculateOrderTotals($order_id);

                    $json['success'] = 'Ваучерът е приложен успешно';
                    $json['totals'] = $updated_totals;
                    $json['debug'] = [
                        'voucher_amount' => $voucher_info['amount'],
                        'totals_count' => count($updated_totals)
                    ];
                } else {
                    $json['error'] = 'Невалиден или неактивен ваучер код';
                }
            } catch (Exception $e) {
                $json['error'] = 'Грешка при прилагане на ваучера: ' . $e->getMessage();
            }
        }

        if($flag_no_ajax) {
            return $json;
        } else {
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * AJAX метод за прилагане на купон код
     */
    public function applyCoupon($order_id = 0, $coupon_code = '') {
        $flag_no_ajax = false;
        if($order_id && $coupon_code) {
            $flag_no_ajax = true;
        }
        else {
            $this->initAdminData();
        }

        $order_id = $order_id ? $order_id : (int)$this->requestPost('order_id', 0);
        $coupon_code = $coupon_code ? $coupon_code : trim($this->requestPost('coupon_code', ''));

        $json = [];

        if (!$order_id) {
            $json['error'] = 'Невалиден ID на поръчка';
        } elseif (empty($coupon_code)) {
            $json['error'] = 'Моля въведете купон код';
        } else {
            try {
                // Опит за зареждане на модела за купони
                try {
                    $this->loadModelAs('extension/total/coupon', 'couponModel');
                    $coupon_info = $this->couponModel->getCoupon($coupon_code);
                } catch (Exception $e) {
                    // Fallback - директна проверка в базата данни
                    $coupon_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "coupon WHERE code = '" . $this->db->escape($coupon_code) . "' AND status = '1'");
                    $coupon_info = $coupon_query->num_rows ? $coupon_query->row : false;
                }

                if ($coupon_info && (isset($coupon_info['status']) ? $coupon_info['status'] : true)) {
                    // Прилагане на купона към поръчката
                    $this->applyCouponToOrder($order_id, $coupon_code, $coupon_info);

                    // Преизчисляване на общите суми
                    $updated_totals = $this->recalculateOrderTotals($order_id);

                    $json['success'] = 'Купонът е приложен успешно';
                    $json['totals'] = $updated_totals;
                    $json['debug'] = [
                        'coupon_type' => $coupon_info['type'],
                        'coupon_discount' => $coupon_info['discount'],
                        'totals_count' => count($updated_totals)
                    ];
                } else {
                    $json['error'] = 'Невалиден или неактивен купон код';
                }
            } catch (Exception $e) {
                $json['error'] = 'Грешка при прилагане на купона: ' . $e->getMessage();
            }
        }

        if($flag_no_ajax) {
            return $json;
        } else {
            $this->setJSONResponseOutput($json);
        }
    }
    /**
     * AJAX: Премахва купон от order_total и връща преизчислени суми
     */
    public function removeCouponAjax() {
        $this->initAdminData();
        $order_id = (int)$this->requestPost('order_id', 0);
        $json = [];
        try {
            if (!$order_id) throw new \Exception('Невалиден ID на поръчка');
            $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'coupon'");
            // Преизчисли тотала според останалите суми
            $updated = $this->recalculateOrderTotals($order_id);
            $json['success'] = true;
            $json['totals'] = $updated;
        } catch (\Exception $e) {
            $json['error'] = 'Грешка при премахване на купона: ' . $e->getMessage();
        }
        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX: Премахва ваучер от order_total и връща преизчислени суми
     */
    public function removeVoucherAjax() {
        $this->initAdminData();
        $order_id = (int)$this->requestPost('order_id', 0);
        $json = [];
        try {
            if (!$order_id) throw new \Exception('Невалиден ID на поръчка');
            $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'voucher'");
            $updated = $this->recalculateOrderTotals($order_id);
            $json['success'] = true;
            $json['totals'] = $updated;
        } catch (\Exception $e) {
            $json['error'] = 'Грешка при премахване на ваучера: ' . $e->getMessage();
        }
        $this->setJSONResponseOutput($json);
    }


    /**
     * Прилага ваучер към поръчката
     */
    private function applyVoucherToOrder($order_id, $voucher_code, $voucher_info) {
        // Премахване на съществуващи ваучери
        $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'voucher'");

        // Гарантиране, че стойността е отрицателна (отстъпка)
        $voucher_amount = abs((float)$voucher_info['amount']) * -1;

        // Добавяне на новия ваучер
        $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
            order_id = '" . (int)$order_id . "',
            code = 'voucher',
            title = 'Ваучер (" . $this->db->escape($voucher_code) . ")',
            value = '" . (float)$voucher_amount . "',
            sort_order = '8'");
    }

    /**
     * Прилага купон към поръчката
     */
    private function applyCouponToOrder($order_id, $coupon_code, $coupon_info) {
        // Премахване на съществуващи купони
        $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'coupon'");

        // Изчисляване на отстъпката
        $discount_amount = $this->calculateCouponDiscount($order_id, $coupon_info);

        // Гарантиране, че стойността е отрицателна (отстъпка)
        $coupon_amount = abs((float)$discount_amount) * -1;

        // Добавяне на новия купон
        $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
            order_id = '" . (int)$order_id . "',
            code = 'coupon',
            title = 'Купон (" . $this->db->escape($coupon_code) . ")',
            value = '" . (float)$coupon_amount . "',
            sort_order = '9'");
    }

    /**
     * Изчислява отстъпката от купон
     */
    private function calculateCouponDiscount($order_id, $coupon_info) {
        // Получаване на междинната сума
        $subtotal_query = $this->db->query("SELECT value FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'sub_total'");
        $subtotal = $subtotal_query->num_rows ? (float)$subtotal_query->row['value'] : 0;

        if ($subtotal <= 0) {
            return 0;
        }

        $discount_amount = 0;

        if ($coupon_info['type'] == 'F') {
            // Фиксирана сума - не може да бъде повече от междинната сума
            $discount_amount = min((float)$coupon_info['discount'], $subtotal);
        } else {
            // Процентна отстъпка (P)
            $discount_amount = ($subtotal * (float)$coupon_info['discount']) / 100;
            // Ограничаване до максимум междинната сума
            $discount_amount = min($discount_amount, $subtotal);
        }

        // Гарантиране, че отстъпката не е отрицателна
        return max(0, $discount_amount);
    }

    /**
     * Преизчислява общите суми на поръчката
     */
    private function recalculateOrderTotals($order_id) {
        // Получаване на всички суми
        $totals_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' ORDER BY sort_order");

        $subtotal = 0;
        $shipping = 0;
        $voucher = 0;
        $coupon = 0;
        $tax = 0;
        $other_totals = 0;

        foreach ($totals_query->rows as $total) {
            switch ($total['code']) {
                case 'sub_total':
                    $subtotal = (float)$total['value'];
                    break;
                case 'shipping':
                    $shipping = (float)$total['value'];
                    break;
                case 'voucher':
                    $voucher = (float)$total['value']; // Вече отрицателна стойност
                    break;
                case 'coupon':
                    $coupon = (float)$total['value']; // Вече отрицателна стойност
                    break;
                case 'tax':
                    $tax = (float)$total['value'];
                    break;
                case 'total':
                    // Пропускаме total - ще го изчислим наново
                    break;
                default:
                    // Други суми (например handling, low_order_fee, etc.)
                    $other_totals += (float)$total['value'];
                    break;
            }
        }

        // Изчисляване на новата обща сума
        // Формула: Междинна сума + Доставка + Данък + Други суми + Ваучер (отрицателен) + Купон (отрицателен)
        $new_total = $subtotal + $shipping + $tax + $other_totals + $voucher + $coupon;

        // Гарантиране, че общата сума не е отрицателна
        $new_total = max(0, $new_total);

        // Актуализиране на общата сума в базата данни
        $this->db->query("UPDATE " . DB_PREFIX . "order_total SET
            value = '" . (float)$new_total . "',
            title = 'Общо'
            WHERE order_id = '" . (int)$order_id . "' AND code = 'total'");

        // Ако няма запис за total, създаваме го
        $total_exists = $this->db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'total'");
        if ($total_exists->row['count'] == 0) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
                order_id = '" . (int)$order_id . "',
                code = 'total',
                title = 'Общо',
                value = '" . (float)$new_total . "',
                sort_order = '99'");
        }

        // Актуализиране на общата сума в основната order таблица
        $this->db->query("UPDATE " . DB_PREFIX . "order SET total = '" . (float)$new_total . "' WHERE order_id = '" . (int)$order_id . "'");

        // Връщане на актуализираните суми
        $this->prepareOrderTotals($order_id);
        return $this->getData('order_totals');
    }

    /**
     * AJAX метод за преизчисляване на общите суми с правилно валутно форматиране
     */
    public function recalculateOrderTotalsAjax() {
        $this->initAdminData();

        $order_id = (int)$this->requestPost('order_id', 0);
        // Продуктите пристигат като products[index][field] във FormData
        $products_data = $this->requestPost('products', []);


        $json = [];

        if (!$order_id) {
            $json['error'] = 'Невалиден ID на поръчка';
        } else {
            try {
                // Подготовка на данните за поръчката
                $this->prepareOrderData($order_id);

                // Изчисляване на новите суми на базата на продуктите (останалите суми от БД)
                $calculated_totals = $this->calculateTotalsFromProducts($order_id, $products_data);

        // Debug: междинна сума след калкулация
        F()->log->developer(['subtotal' => $calculated_totals['sub_total']], __FILE__, __LINE__);

                // Форматиране на сумите с правилната валута
                $formatted_totals = $this->formatOrderTotals($calculated_totals);

                $json['success'] = true;
                $json['totals'] = $formatted_totals;

            } catch (Exception $e) {
                $json['error'] = 'Грешка при преизчисляване на сумите: ' . $e->getMessage();
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Изчислява общите суми на базата на продуктите
     */
    private function calculateTotalsFromProducts($order_id, $products_data, $return_like_db_records = false) {
        // Изчисляване на междинната сума от продуктите
        $subtotal = 0;

        if (!empty($products_data)) {
            foreach ($products_data as $product) {
                $quantity = isset($product['quantity']) ? (float)$product['quantity'] : 0;
                $price = isset($product['price']) ? (float)$product['price'] : 0;
                $subtotal += $quantity * $price;
            }
        } else {
            // Ако няма подадени продукти, приемаме междинна сума 0.00
            $subtotal = 0;
        }

        // Получаване на съществуващите суми от базата данни
        $order_totals = $this->orders->getOrderTotals($order_id);

        // Инициализация с текущите стойности
        $shipping = 0;
        $tax = 0;
        $voucher = 0;
        $coupon = 0;
        $other_totals = 0;

        foreach ($order_totals as $total) {
            $value = (float)$total['value'];

            switch ($total['code']) {
                case 'shipping':
                    $shipping = $value;
                    break;
                case 'tax':
                    $tax = $value;
                    break;
                case 'voucher':
                    $voucher = $value; // Вече отрицателна стойност
                    break;
                case 'coupon':
                    $coupon = $value; // Вече отрицателна стойност
                    break;
                case 'sub_total':
                case 'total':
                    // Пропускаме - ще ги изчислим наново
                    break;
                default:
                    // Други суми
                    $other_totals += $value;
                    break;
            }
        }


        // Ако няма продукти (subtotal == 0) и всички продукти са изтрити, нулираме тоталите
        $noProducts = empty($products_data);
        if ($noProducts) {
            $shipping = 0;
            $tax = 0;
            $voucher = 0;
            $coupon = 0;
            $other_totals = 0;
        }

        // Изчисляване на крайната сума
        $total = $subtotal + $shipping + $tax + $other_totals + $voucher + $coupon;
        $total = max(0, $total);

        return [
            'sub_total' => $subtotal,
            'shipping' => $shipping,
            'tax' => $tax,
            'voucher' => $voucher,
            'coupon' => $coupon,
            'other_totals' => $other_totals,
            'total' => $total
        ];
    }

    /**
     * Форматира общите суми с правилната валута
     */
    private function formatOrderTotals($calculated_totals) {
        $currency_code = $this->getData('currency_code');
        $currency_value = $this->getData('currency_value');

        $formatted_totals = [];

        // Междинна сума
        if ($calculated_totals['sub_total'] > 0) {
            $formatted_totals[] = [
                'code' => 'sub_total',
                'title' => 'Цена на продуктите',
                'text' => $this->formatCurrency($calculated_totals['sub_total'], $currency_code, $currency_value),
                'value' => $calculated_totals['sub_total']
            ];
        }

        // Доставка
        if ($calculated_totals['shipping'] != 0) {
            $formatted_totals[] = [
                'code' => 'shipping',
                'title' => 'Безплатна доставка',
                'text' => $this->formatCurrency($calculated_totals['shipping'], $currency_code, $currency_value),
                'value' => $calculated_totals['shipping']
            ];
        }

        // Данък
        if ($calculated_totals['tax'] != 0) {
            $formatted_totals[] = [
                'code' => 'tax',
                'title' => 'Данък',
                'text' => $this->formatCurrency($calculated_totals['tax'], $currency_code, $currency_value),
                'value' => $calculated_totals['tax']
            ];
        }

        // Други суми
        if ($calculated_totals['other_totals'] != 0) {
            $formatted_totals[] = [
                'code' => 'other',
                'title' => 'Други такси',
                'text' => $this->formatCurrency($calculated_totals['other_totals'], $currency_code, $currency_value),
                'value' => $calculated_totals['other_totals']
            ];
        }

        // Ваучер отстъпка
        if ($calculated_totals['voucher'] != 0) {
            $formatted_totals[] = [
                'code' => 'voucher',
                'title' => 'Код за отстъпка (ваучер)',
                'text' => $this->formatCurrency($calculated_totals['voucher'], $currency_code, $currency_value),
                'value' => $calculated_totals['voucher']
            ];
        }

        // Купон отстъпка
        if ($calculated_totals['coupon'] != 0) {
            $formatted_totals[] = [
                'code' => 'coupon',
                'title' => 'Код за отстъпка (купон)',
                'text' => $this->formatCurrency($calculated_totals['coupon'], $currency_code, $currency_value),
                'value' => $calculated_totals['coupon']
            ];
        }

        // Обща сума
        $formatted_totals[] = [
            'code' => 'total',
            'title' => 'Общо',
            'text' => $this->formatCurrency($calculated_totals['total'], $currency_code, $currency_value),
            'value' => $calculated_totals['total']
        ];

        return $formatted_totals;
    }

}
